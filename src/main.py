import os
import sys
import argparse
from dotenv import load_dotenv

from src.rag.create_vector_store import CreateVectorStore
from src.rag.retrieve_doc_cassandra import RetrieveDoc
from src.utils.file_utils import FileUtils
from src.utils.logger import Logger
from src.rag.gr_rag_chat_service import GrRagChatService
from src.rag.rest_rag_chat_service import RestRagChatService

logging = Logger(__name__).get()

def init_env() -> None:
    load_dotenv(override=True)
    os.environ["TOKENIZERS_PARALLELISM"] = "false"
    os.environ['OPENAI_API_KEY'] = os.getenv('OPENAI_API_KEY', 'your-key-if-not-using-env')
    os.environ['HF_TOKEN'] = os.getenv('HF_TOKEN', 'your-key-if-not-using-env')

def init_case_env(case: str) -> None:
    os.makedirs(FileUtils.get_data_dir(), exist_ok = True)
    os.makedirs(FileUtils.get_case_dir(case), exist_ok = True)
    os.makedirs(FileUtils.get_documents_dir(case), exist_ok = True)
    os.makedirs(FileUtils.get_patents_dir(case), exist_ok = True)
    os.makedirs(FileUtils.get_articles_dir(case), exist_ok = True)

def handle_gr_chat(args: argparse.Namespace) -> None:
    logging.info(f"Starting GR RAG chat server for case {args.case}...")
    init_case_env(args.case)
    gr_rag_chat = GrRagChatService(args.case)
    gr_rag_chat.launch()

def handle_rest_chat(args: argparse.Namespace) -> None:
    logging.info(f"Starting REST RAG chat server for case {args.case}...")
    init_case_env(args.case)
    gr_rag_chat = RestRagChatService(args.case)
    gr_rag_chat.run()

def handle_retrieve_docs(args: argparse.Namespace) -> None:
    logging.info(f"Retrieving documents for case {args.case}...")
    init_case_env(args.case)
    retrieve_doc = RetrieveDoc(args.case)
    retrieve_doc.retrieve_and_store_documents(args.search_term)

def handle_create_vector_store(args: argparse.Namespace) -> None:
    logging.info(f"Creating vector store for case {args.case}...")
    init_case_env(args.case)
    create_vector_store = CreateVectorStore(args.case)
    create_vector_store.create_vector_store()

def build_parser() -> argparse.ArgumentParser:
    parser = argparse.ArgumentParser(
        prog="percipio-ai-rag",
        description="Percipio AI RAG tools",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )
    sub = parser.add_subparsers(dest="command", required=True)

    init_env()

    # Gradio chat
    run_p = sub.add_parser("gr-chat", help="Handle gradio classic chat")
    run_p.set_defaults(func = handle_gr_chat)
    run_p.add_argument("case", help="Case to use")

    # Rest chat
    run_p = sub.add_parser("rest-chat", help="Handle rest chat")
    run_p.set_defaults(func = handle_rest_chat)
    run_p.add_argument("case", help="Case to use")

    # Retrieve documents
    run_p = sub.add_parser("retrieve-docs", help="Retrieve documents")
    run_p.set_defaults(func = handle_retrieve_docs)
    run_p.add_argument("case", help="Case to use")
    run_p.add_argument("search_term", help="Search term which is based of case")

    # Create vector store
    run_p = sub.add_parser("create-vector-store", help="Create vector store")
    run_p.set_defaults(func=handle_create_vector_store)
    run_p.add_argument("case", help="Case to use")

    return parser

def main(argv: list[str] | None = None) -> None:
    """Entry point — parse CLI and dispatch."""
    args = build_parser().parse_args(argv)
    args.func(args)          # call the handler bound via set_defaults

if __name__ == "__main__":
    main(sys.argv[1:])