import os
import pickle
from pathlib import Path

from src.utils.constants import documents_dir, db_dir, articles_pickle_filename, patents_pickle_filename, \
    patents_name, articles_name
from src.utils.logger import Logger

from src.utils.constants import data_dir

logging = Logger(__name__).get()

class FileUtils:
    @staticmethod
    def find_files(root_folder: str | Path, image_filter, recursive: bool = True) -> list[Path]:
        """
        Recursively find also files with specific image_filter like *.jpg
        """
        root = Path(root_folder)
        if recursive:
            return list(root.rglob(image_filter))  # Case-sensitive
        else:
            return list(root.glob(image_filter))  # Case-sensitive

    @staticmethod
    def get_data_dir() -> str:
        return os.path.join(os.path.dirname(__file__), data_dir)

    @staticmethod
    def get_case_dir(case: str) -> str:
        return os.path.join(FileUtils.get_data_dir(), case)

    @staticmethod
    def get_documents_dir(case: str) -> str:
         return os.path.join(FileUtils.get_case_dir(case), documents_dir)

    @staticmethod
    def get_patents_dir(case: str) -> str:
        return os.path.join(FileUtils.get_documents_dir(case), patents_name)

    @staticmethod
    def get_articles_dir(case: str) -> str:
        return os.path.join(FileUtils.get_documents_dir(case), articles_name)

    @staticmethod
    def get_vectorstore_db_file(case: str) -> str:
        return os.path.join(FileUtils.get_case_dir(case), db_dir)

    @staticmethod
    def get_articles_pickle_file(case: str) -> str:
        return os.path.join(FileUtils.get_case_dir(case), articles_pickle_filename)

    @staticmethod
    def get_patents_pickle_file(case: str) -> str:
        return os.path.join(FileUtils.get_case_dir(case), patents_pickle_filename)

    @staticmethod
    def file_exists(file_dir, file_name) -> bool:
        return os.path.exists(os.path.join(file_dir, file_name))

    @staticmethod
    def save_pickle_file(pickle_file, items) -> None:
        with open(pickle_file, 'wb') as f:
            pickle.dump(items, f)
        logging.info(f"\nSaved {len(items)} documents to {pickle_file}")

    @staticmethod
    def load_items(pickle_file):
        logging.info(f"Loading data from {pickle_file}...")
        with open(pickle_file, 'rb') as file:
            items = pickle.load(file)
        return items


