import os
import re
from dataclasses import dataclass, field
from typing import List, Dict, Optional

from src.utils.logger import Logger

logging = Logger(__name__).get()

@dataclass
class ItemArticle:
    """Class representing an article with all its metadata."""
    id: str
    title: str = "Untitled Article"
    year: Optional[int] = None
    authors: List[str] = field(default_factory=list)
    author2org: Dict[str, str] = field(default_factory=dict)
    summary: str = "No abstract provided."
    doi: Optional[str] = None
    urls: List[str] = field(default_factory=list)

    @classmethod
    def from_cassandra_row(cls, row):
        """Create an Item from a Cassandra row."""
        data = row._asdict()
        return cls(
            id = data.get("id", ""),
            title = _clean_tags(data.get("title", "Untitled Article")),
            year = data.get("year"),
            authors = data.get("authors", []),
            author2org = data.get("author2org", {}),
            summary = _clean_tags(data.get("summary", "No abstract provided.")),
            doi = data.get("doi", ""),
            urls = data.get("urls", [])
        )

    def to_markdown(self, output_dir = "knowledge-base/article"):
        """Convert the item to Markdown format and save to file."""
        # Handle authors and affiliations
        authors_str = ", ".join(self.authors)

        # Create affiliations list
        affiliations = []
        if hasattr(self.author2org, "items"):
            affiliations = [
                f"{author}: {_clean_tags(org)}"
                for author, org in self.author2org.items()
                if org  # Only include if org is non-empty
            ]

        # Only include affiliations section if there are any affiliations
        affiliation_section = ""
        if affiliations:
            affiliation_str = "\n".join([f"- {a}" for a in affiliations])
            affiliation_section = f"""**Affiliation:**  
{affiliation_str}
"""

        # Get DOI if available
        doi_section = ""
        if self.doi:
            doi_section = f"**DOI:** {self.doi}\n"

        # Get URLs if available
        urls_section = ""
        if self.urls:
            # Format URLs as clickable markdown links
            formatted_urls = []
            for url in self.urls:
                formatted_urls.append(f"[{url}]({url})")

            urls_links = ", ".join(formatted_urls)
            urls_section = f"**Links:** {urls_links}\n"

        # Create markdown content
        markdown = f"""# {self.title}

**Author(s):** {authors_str}  
**Date:** {self.year}  
{affiliation_section}
{doi_section}
{urls_section}
---

## Abstract

{self.summary}

---
"""

        # Ensure output directory exists
        os.makedirs(output_dir, exist_ok=True)

        # Generate filename with next available number
        filename = _get_next_article_filename(output_dir)

        # Save to file
        filepath = os.path.join(output_dir, filename)
        with open(filepath, "w", encoding="utf-8") as f:
            f.write(markdown)

        logging.info(f"✔ Saved: {filepath}")
        return filepath


@dataclass
class ItemPatent:
    """Class representing a patent with all its metadata."""
    id: str
    title: str = "Untitled Patent"
    year: Optional[str] = None
    applicants: List[str] = field(default_factory=list)
    applicant2org: Dict[str, str] = field(default_factory=dict)
    inventors: List[str] = field(default_factory=list)
    inventor2org: Dict[str, str] = field(default_factory=dict)
    summary: str = "No abstract provided."
    application_date: Optional[str] = None
    priority_date: Optional[str] = None
    publishing_date: Optional[str] = None
    country: Optional[str] = None
    office: Optional[str] = None
    lang: Optional[str] = None
    patents: List[str] = field(default_factory=list)
    references: List[str] = field(default_factory=list)

    @classmethod
    def from_cassandra_row(cls, row):
        """Create an ItemPatent from a Cassandra row."""
        data = row._asdict()
        return cls(
            id = data.get("id", ""),
            title = _clean_tags(data.get("title", "Untitled Patent")),
            year = data.get("year"),
            applicants = data.get("applicants", []),
            applicant2org = data.get("applicant2org", {}),
            inventors = data.get("inventors", []),
            inventor2org = data.get("inventor2org", {}),
            summary = _clean_tags(data.get("summary", "No summary provided.")),
            application_date = data.get("application_date"),
            priority_date = data.get("priority_date"),
            publishing_date = data.get("publishing_date"),
            country = data.get("country"),
            office = data.get("office"),
            lang = data.get("lang"),
            patents = data.get("patents", []),
            references = data.get("references", [])
        )

    def to_markdown(self, output_dir="knowledge-base/patent"):
        """Convert the patent to Markdown format and save to file."""
        # Handle applicants and their organizations
        applicants_str = ", ".join(self.applicants)

        # Handle inventors and their organizations
        inventors_str = ", ".join(self.inventors)

        # Create applicant affiliations list
        applicant_affiliations = []
        if hasattr(self.applicant2org, "items"):
            applicant_affiliations = [
                f"{applicant}: {_clean_tags(org)}"
                for applicant, org in self.applicant2org.items()
                if org  # Only include if org is non-empty
            ]

        # Create inventor affiliations list
        inventor_affiliations = []
        if hasattr(self.inventor2org, "items"):
            inventor_affiliations = [
                f"{inventor}: {_clean_tags(org)}"
                for inventor, org in self.inventor2org.items()
                if org  # Only include if org is non-empty
            ]

        # Only include applicant affiliations section if there are any
        applicant_affiliation_section = ""
        if applicant_affiliations:
            affiliation_str = "\n".join([f"- {a}" for a in applicant_affiliations])
            applicant_affiliation_section = f"""**Applicant Organizations:**
{affiliation_str}

"""

        # Only include inventor affiliations section if there are any
        inventor_affiliation_section = ""
        if inventor_affiliations:
            affiliation_str = "\n".join([f"- {a}" for a in inventor_affiliations])
            inventor_affiliation_section = f"""**Inventor Organizations:**
{affiliation_str}

"""

        # Get dates if available
        dates_section = ""
        if self.application_date:
            dates_section += f"**Application Date:** {self.application_date}  \n"
        if self.priority_date:
            dates_section += f"**Priority Date:** {self.priority_date}  \n"
        if self.publishing_date:
            dates_section += f"**Publishing Date:** {self.publishing_date}  \n"

        # Get location info if available
        location_section = ""
        if self.country:
            location_section += f"**Country:** {self.country}  \n"
        if self.office:
            location_section += f"**Patent Office:** {self.office}  \n"
        if self.lang:
            location_section += f"**Language:** {self.lang}  \n"

        # Get patent numbers if available
        patents_section = ""
        if self.patents:
            patents_str = ", ".join(self.patents)
            patents_section = f"**Patent Numbers:** {patents_str}  \n"

        # Get references if available
        references_section = ""
        if self.references:
            # Limit to first 10 references to avoid overly long sections
            ref_list = self.references[:10]
            if len(self.references) > 10:
                ref_list.append(f"... and {len(self.references) - 10} more")
            references_str = ", ".join(ref_list)
            references_section = f"**References:** {references_str}  \n"

        # Create markdown content
        markdown = f"""# {self.title}

**Applicant(s):** {applicants_str}
**Inventor(s):** {inventors_str}
**Year:** {self.year}
{dates_section}
{location_section}
{patents_section}
{applicant_affiliation_section}
{inventor_affiliation_section}
{references_section}
---

## Summary

{self.summary}

---
"""

        # Ensure output directory exists
        os.makedirs(output_dir, exist_ok=True)

        # Generate filename with next available number
        filename = _get_next_patent_filename(output_dir)

        # Save to file
        filepath = os.path.join(output_dir, filename)
        with open(filepath, "w", encoding="utf-8") as f:
            f.write(markdown)

        logging.info(f"✔ Saved: {filepath}")
        return filepath


def patent_cassandra_row_to_markdown(row, output_dir="knowledge-base/patent"):
    """Convert Cassandra row data to Markdown format and save to file.

    Args:
        row: Cassandra row containing patent data
        output_dir: Directory to save the markdown file

    Returns:
        filepath: Path to the saved markdown file
    """
    item = ItemPatent.from_cassandra_row(row)
    return item.to_markdown(output_dir)


def article_cassandra_row_to_markdown(row, output_dir="knowledge-base/article"):
    """Convert Cassandra row data to Markdown format and save to file.

    Args:
        row: Cassandra row containing article data
        output_dir: Directory to save the markdown file

    Returns:
        filepath: Path to the saved markdown file
    """
    item = ItemArticle.from_cassandra_row(row)
    return item.to_markdown(output_dir)


def _clean_tags(text):
    """Remove XML/JATS tags from text.

    Args:
        text: Text that may contain XML/JATS tags

    Returns:
        Cleaned text without tags
    """
    if not text:
        return ""

    # Remove XML/JATS tags like <jats:title>, <jats:p>, etc.
    text = re.sub(r'</?[a-z]+:[^>]+>', '', text)

    # Remove regular HTML/XML tags
    text = re.sub(r'</?[^>]+>', '', text)

    # Remove common abstract headings if they exist at the beginning
    # Case insensitive match for Abstract, Summary, or Background
    text = re.sub(r'(?i)^(abstract|summary|background)\s*:?\s*', '', text)

    # Clean up extra whitespace
    text = re.sub(r'\s+', ' ', text).strip()

    return text


def _get_next_article_filename(output_dir):
    """Generate the next available article filename in sequence."""
    existing_files = [
        f for f in os.listdir(output_dir)
        if f.startswith("article-") and f.endswith(".md")
    ]

    numbers = [
        int(re.search(r'article-(\d+)\.md', f).group(1))
        for f in existing_files if re.search(r'article-(\d+)\.md', f)
    ]

    next_number = max(numbers) + 1 if numbers else 1
    return f"article-{next_number:05d}.md"


def _get_next_patent_filename(output_dir):
    """Generate the next available patent filename in sequence."""
    existing_files = [
        f for f in os.listdir(output_dir)
        if f.startswith("patent-") and f.endswith(".md")
    ]

    numbers = [
        int(re.search(r'patent-(\d+)\.md', f).group(1))
        for f in existing_files if re.search(r'patent-(\d+)\.md', f)
    ]

    next_number = max(numbers) + 1 if numbers else 1
    return f"patent-{next_number:05d}.md"