import os
import re
from dataclasses import dataclass, field
from typing import List, Dict, Optional, Union

from src.utils.logger import Logger

logging = Logger(__name__).get()

@dataclass
class ItemDocument:
    """Unified class representing both articles and patents with all their metadata."""
    id: str
    type: str  # "article" or "patent"
    title: str = "Untitled Document"
    year: Optional[str] = None
    summary: str = "No abstract provided."
    doi: Optional[str] = None
    source: Optional[str] = None
    publisher: Optional[str] = None
    volume: Optional[str] = None
    issue: Optional[str] = None
    lang: Optional[str] = None
    issn: List[str] = field(default_factory=list)
    urls: List[str] = field(default_factory=list)
    authors: List[Dict[str, str]] = field(default_factory=list)
    references: List[str] = field(default_factory=list)
    fos: List[str] = field(default_factory=list)
    
    # Patent-specific fields
    assignee: List[Dict[str, str]] = field(default_factory=list)
    publication_date: Optional[str] = None
    priority_date: Optional[str] = None
    date: Optional[str] = None

    @classmethod
    def from_elasticsearch_source(cls, source: Dict):
        """Create an ItemDocument from an Elasticsearch _source document."""
        return cls(
            id=source.get("id", ""),
            type=source.get("type", ""),
            title=_clean_tags(source.get("title", "Untitled Document")),
            year=source.get("year"),
            summary=_clean_tags(source.get("summary", "No abstract provided.")),
            doi=source.get("doi", ""),
            source=source.get("source", ""),
            publisher=source.get("publisher", ""),
            volume=source.get("volume", ""),
            issue=source.get("issue", ""),
            lang=source.get("lang", ""),
            issn=source.get("issn", []),
            urls=source.get("urls", []),
            authors=source.get("authors", []),
            references=source.get("references", []),
            fos=source.get("fos", []),
            assignee=source.get("assignee", []),
            publication_date=source.get("publicationDate"),
            priority_date=source.get("priorityDate"),
            date=source.get("date")
        )

    def get_author_names(self) -> List[str]:
        """Extract author names from the authors list."""
        return [author.get("name", "") for author in self.authors if author.get("name")]

    def get_assignee_names(self) -> List[str]:
        """Extract assignee names from the assignee list (for patents)."""
        return [assignee.get("name", "") for assignee in self.assignee if assignee.get("name")]

    def to_markdown(self, output_dir: str = None) -> str:
        """Convert the document to Markdown format and save to file."""
        if output_dir is None:
            output_dir = f"knowledge-base/{self.type}"
            
        if self.type == "article":
            return self._article_to_markdown(output_dir)
        elif self.type == "patent":
            return self._patent_to_markdown(output_dir)
        else:
            logging.warning(f"Unknown document type: {self.type}")
            return self._generic_to_markdown(output_dir)

    def _article_to_markdown(self, output_dir: str) -> str:
        """Convert article to Markdown format."""
        # Handle authors
        author_names = self.get_author_names()
        authors_str = ", ".join(author_names)

        # Create author organizations list
        author_orgs = []
        for author in self.authors:
            name = author.get("name", "")
            org = author.get("org", "")
            if name and org:
                author_orgs.append(f"{name}: {_clean_tags(org)}")

        # Author organizations section
        author_org_section = ""
        if author_orgs:
            org_str = "\n".join([f"- {a}" for a in author_orgs])
            author_org_section = f"""**Author Organizations:**  
{org_str}

"""

        # DOI section
        doi_section = ""
        if self.doi:
            doi_section = f"**DOI:** {self.doi}  \n"

        # Source and publication info
        pub_info = []
        if self.source:
            pub_info.append(f"**Source:** {self.source}")
        if self.publisher:
            pub_info.append(f"**Publisher:** {self.publisher}")
        if self.volume:
            pub_info.append(f"**Volume:** {self.volume}")
        if self.issue:
            pub_info.append(f"**Issue:** {self.issue}")
        if self.issn:
            issn_str = ", ".join(self.issn)
            pub_info.append(f"**ISSN:** {issn_str}")
        
        pub_section = ""
        if pub_info:
            pub_section = "  \n".join(pub_info) + "  \n"

        # URLs section
        urls_section = ""
        if self.urls:
            formatted_urls = [f"[{url}]({url})" for url in self.urls]
            urls_links = ", ".join(formatted_urls)
            urls_section = f"**Links:** {urls_links}  \n"

        # Create markdown content
        markdown = f"""# {self.title}

**Author(s):** {authors_str}  
**Year:** {self.year}  
{doi_section}
{pub_section}
{author_org_section}
{urls_section}
---

## Abstract

{self.summary}

---
"""

        return self._save_markdown(markdown, output_dir, "article")

    def _patent_to_markdown(self, output_dir: str) -> str:
        """Convert patent to Markdown format."""
        # Handle authors (inventors for patents)
        inventor_names = self.get_author_names()
        inventors_str = ", ".join(inventor_names)

        # Handle assignees
        assignee_names = self.get_assignee_names()
        assignees_str = ", ".join(assignee_names)

        # Create inventor organizations list (authors field contains inventors for patents)
        inventor_orgs = []
        for author in self.authors:
            name = author.get("name", "")
            org = author.get("org", "")
            if name and org:
                inventor_orgs.append(f"{name}: {org}")

        # Create assignee organizations list
        assignee_orgs = []
        for assignee in self.assignee:
            name = assignee.get("name", "")
            org = assignee.get("org", "")
            if name and org:
                assignee_orgs.append(f"{name}: {org}")

        # Inventor organizations section
        inventor_org_section = ""
        if inventor_orgs:
            org_str = "\n".join([f"- {a}" for a in inventor_orgs])
            inventor_org_section = f"""**Inventor Organizations:**
{org_str}

"""

        # Assignee organizations section
        assignee_org_section = ""
        if assignee_orgs:
            org_str = "\n".join([f"- {a}" for a in assignee_orgs])
            assignee_org_section = f"""**Assignee Organizations:**
{org_str}

"""

        # Dates section
        dates_section = ""
        if self.date:
            dates_section += f"**Date:** {self.date}  \n"
        if self.publication_date:
            dates_section += f"**Publication Date:** {self.publication_date}  \n"
        if self.priority_date:
            dates_section += f"**Priority Date:** {self.priority_date}  \n"

        # Source info
        source_section = ""
        if self.source:
            source_section = f"**Source:** {self.source}  \n"

        # URLs section
        urls_section = ""
        if self.urls:
            formatted_urls = [f"[{url}]({url})" for url in self.urls]
            urls_links = ", ".join(formatted_urls)
            urls_section = f"**Links:** {urls_links}  \n"

        # Create markdown content
        markdown = f"""# {self.title}

**Inventor(s):** {inventors_str}
**Assignee(s):** {assignees_str}
**Year:** {self.year}
{dates_section}
{source_section}
{inventor_org_section}
{assignee_org_section}
{urls_section}
---

## Summary

{self.summary}

---
"""

        return self._save_markdown(markdown, output_dir, "patent")

    def _generic_to_markdown(self, output_dir: str) -> str:
        """Convert generic document to Markdown format."""
        author_names = self.get_author_names()
        authors_str = ", ".join(author_names)

        markdown = f"""# {self.title}

**Type:** {self.type}  
**Author(s):** {authors_str}  
**Year:** {self.year}  

---

## Summary

{self.summary}

---
"""

        return self._save_markdown(markdown, output_dir, self.type)

    def _save_markdown(self, markdown: str, output_dir: str, doc_type: str) -> str:
        """Save markdown content to file."""
        # Ensure output directory exists
        os.makedirs(output_dir, exist_ok=True)

        # Generate filename with next available number
        if doc_type == "article":
            filename = _get_next_article_filename(output_dir)
        elif doc_type == "patent":
            filename = _get_next_patent_filename(output_dir)
        else:
            filename = _get_next_generic_filename(output_dir, doc_type)

        # Save to file
        filepath = os.path.join(output_dir, filename)
        with open(filepath, "w", encoding="utf-8") as f:
            f.write(markdown)

        logging.info(f"✔ Saved: {filepath}")
        return filepath


def _clean_tags(text):
    """Remove XML/JATS tags from text."""
    if not text:
        return ""

    # Remove XML/JATS tags like <jats:title>, <jats:p>, etc.
    text = re.sub(r'</?[a-z]+:[^>]+>', '', text)

    # Remove regular HTML/XML tags
    text = re.sub(r'</?[^>]+>', '', text)

    # Remove common abstract headings if they exist at the beginning
    text = re.sub(r'(?i)^(abstract|summary|background)\s*:?\s*', '', text)

    # Clean up extra whitespace
    text = re.sub(r'\s+', ' ', text).strip()

    return text


def _get_next_article_filename(output_dir):
    """Generate the next available article filename in sequence."""
    existing_files = [
        f for f in os.listdir(output_dir)
        if f.startswith("article-") and f.endswith(".md")
    ]

    numbers = [
        int(re.search(r'article-(\d+)\.md', f).group(1))
        for f in existing_files if re.search(r'article-(\d+)\.md', f)
    ]

    next_number = max(numbers) + 1 if numbers else 1
    return f"article-{next_number:05d}.md"


def _get_next_patent_filename(output_dir):
    """Generate the next available patent filename in sequence."""
    existing_files = [
        f for f in os.listdir(output_dir)
        if f.startswith("patent-") and f.endswith(".md")
    ]

    numbers = [
        int(re.search(r'patent-(\d+)\.md', f).group(1))
        for f in existing_files if re.search(r'patent-(\d+)\.md', f)
    ]

    next_number = max(numbers) + 1 if numbers else 1
    return f"patent-{next_number:05d}.md"


def _get_next_generic_filename(output_dir, doc_type):
    """Generate the next available filename for generic document types."""
    existing_files = [
        f for f in os.listdir(output_dir)
        if f.startswith(f"{doc_type}-") and f.endswith(".md")
    ]

    numbers = [
        int(re.search(rf'{doc_type}-(\d+)\.md', f).group(1))
        for f in existing_files if re.search(rf'{doc_type}-(\d+)\.md', f)
    ]

    next_number = max(numbers) + 1 if numbers else 1
    return f"{doc_type}-{next_number:05d}.md"
