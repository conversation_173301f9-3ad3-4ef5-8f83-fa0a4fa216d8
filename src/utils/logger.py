"""
Reusable logger with colorlog support
"""

from __future__ import annotations
import logging
from typing import Optional

from colorlog import ColoredFormatter

class Logger:
    """
    Wraps Python logging so you get:
      • Color‑coded messages in the terminal (via colorlog)
      • Optional log‑to‑file with a plain formatter
      • One‑line setup: Logger(...).get() → ready‑to‑use logging.Logger
    """

    _STREAM_FORMAT = (
        "%(log_color)s[%(asctime)s] [%(levelname)-8s]%(reset)s | %(message)s"
    )
    _FILE_FORMAT = "[%(asctime)s] [%(levelname)-8s] | %(message)s"
    _DATEFMT = "%Y-%m-%d %H:%M:%S"
    _LOG_COLORS = {
        "DEBUG": "cyan",
        "INFO": "green",
        "WARNING": "yellow",
        "ERROR": "red",
        "CRITICAL": "bold_red",
    }

    def __init__(
        self,
        name: str = "app",
        /,
        *,
        level: int | str = logging.INFO,
        stream: bool = True,
        logfile: Optional[str] = None,
        propagate: bool = False,
    ) -> None:
        """
        Args
        ----
        name      – logger name (typically __name__)
        level     – base logging level (numeric or string, e.g. "DEBUG")
        stream    – add a colorized StreamHandler (stdout)
        logfile   – path to a file for plain text logs (optional)
        propagate – forward records to parent loggers (default False)
        """
        self._logger = logging.getLogger(name)
        self._logger.setLevel(level)
        self._logger.propagate = propagate

        # Prevent duplicate handlers if you create multiple instances
        if not self._logger.handlers:
            if stream:
                self._logger.addHandler(self._build_stream_handler(level))
            if logfile:
                self._logger.addHandler(self._build_file_handler(logfile, level))

    # --------------------------------------------------------------------- #
    # Public helpers
    # --------------------------------------------------------------------- #

    def get(self) -> logging.Logger:  # noqa: D401 (“get logger”)
        """Return the underlying ``logging.Logger`` instance."""
        return self._logger

    # Allow dot‑forwarding: Logger().debug(...) etc.
    def __getattr__(self, item):
        return getattr(self._logger, item)

    # --------------------------------------------------------------------- #
    # Internal helpers
    # --------------------------------------------------------------------- #

    def _build_stream_handler(self, level):
        fmt = ColoredFormatter(
            self._STREAM_FORMAT, datefmt=self._DATEFMT, log_colors=self._LOG_COLORS
        )
        handler = logging.StreamHandler()
        handler.setLevel(level)
        handler.setFormatter(fmt)
        return handler

    def _build_file_handler(self, logfile, level):
        fmt = logging.Formatter(self._FILE_FORMAT, datefmt=self._DATEFMT)
        handler = logging.FileHandler(logfile, encoding="utf-8")
        handler.setLevel(level)
        handler.setFormatter(fmt)
        return handler
