import requests
from typing import List, Tuple

from src.utils.config import es_url_via_cassandra, es_url
from src.utils.file_utils import FileUtils
from src.utils.logger import Logger
from src.utils.article_utils import ItemDocument

logging = Logger(__name__).get()

class RetrieveDoc:
    def __init__(self, case: str):
        self.items_article = []  # Store all retrieved articles
        self.items_patent = []  # Store all retrieved patents
        self.case = case

    def retrieve_documents(self, type: str,search_terms: List[str], max_docs: int = 10000,
                         batch_size: int = 10, sort_type: str = "year", 
                         sort_order: str = "desc") -> Tuple[List[ItemDocument], List[ItemDocument]]:
        """
        Retrieve documents from Elasticsearch using pagination.

        Args:
            search_terms: List of terms to search for in Elasticsearch
            max_docs: Maximum number of documents to retrieve
            batch_size: Number of documents to retrieve per batch
            sort_type: Field to sort by (default: "year")
            sort_order: Sort order "asc" or "desc" (default: "desc")

        Returns:
            Tuple of (articles, patents) as lists of ItemDocument objects
        """
        # Elasticsearch endpoint
        url = es_url

        # Process documents in batches
        processed_count = 0
        items_article = []  # Store all retrieved articles
        items_patent = []  # Store all retrieved patents

        for from_idx in range(0, max_docs, batch_size):
            # Query body with pagination and sorting
            query = {
                "from": from_idx,
                "size": batch_size,
                "query": {
                    "bool": {
                        "must": [
                            # All search terms
                            *[
                                {
                                    "query_string": {
                                        "query": f'lemmas:"{search_term}"'
                                    }
                                }
                                for search_term in search_terms
                            ],
                            # Extra query_string condition
                            {
                                "query_string": {
                                    "query": f'type:{type}'
                                }
                            }
                        ],
                        "should": [],
                        "must_not": []
                    }
                },
                "sort": [
                    {
                        sort_type: {
                            "order": sort_order
                        }
                    }
                ]
            }

            # Send post request
            response = requests.post(url, json=query)

            # Check for success
            if response.status_code == 200:
                data = response.json()
                hits = data.get("hits", {}).get("hits", [])

                # If no more results, break the loop
                if not hits:
                    logging.info(f"No more results found after {processed_count} documents.")
                    break

                logging.info(f"\nProcessing batch {from_idx // batch_size + 1} (documents {from_idx + 1}-{from_idx + len(hits)})")

                for hit in hits:
                    source = hit.get("_source", {})
                    doc_type = source.get("type")
                    doc_id = source.get("id")
                    
                    logging.debug(f"Processing ES document: {doc_id} (type: {doc_type})")

                    try:
                        # Create ItemDocument from Elasticsearch source
                        item = ItemDocument.from_elasticsearch_source(source)
                        
                        # Validate that we have essential fields
                        if not item.title or not item.summary:
                            logging.warning(f"Skipping document {doc_id} - missing title or summary")
                            continue

                        # Separate articles and patents
                        if doc_type == 'article':
                            items_article.append(item)
                            # Generate markdown file
                            item.to_markdown(FileUtils.get_articles_dir(self.case))
                        elif doc_type == 'patent':
                            items_patent.append(item)
                            # Generate markdown file
                            item.to_markdown(FileUtils.get_patents_dir(self.case))
                        else:
                            logging.warning(f"Unknown document type: {doc_type} for document {doc_id}")

                        processed_count += 1

                    except Exception as e:
                        logging.error(f"Error processing document {doc_id}: {e}")
                        continue

            else:
                logging.error(f"Elasticsearch error: {response.status_code} - {response.text}")
                break

        logging.info(f"\nCompleted processing {processed_count} documents.")
        logging.info(f"Retrieved {len(items_article)} articles and {len(items_patent)} patents.")

        return items_article, items_patent

    def retrieve_and_store_documents(self, search_term: str, max_docs: int = 10000,
                                   batch_size: int = 10, sort_type: str = "year", 
                                   sort_order: str = "desc") -> None:
        """
        Retrieve documents and store them as pickle files.

        Args:
            search_term: Search term (will be split on spaces)
            max_docs: Maximum number of documents to retrieve
            batch_size: Number of documents to retrieve per batch
            sort_type: Field to sort by (default: "year")
            sort_order: Sort order "asc" or "desc" (default: "desc")
        """
        search_terms = search_term.split(" ")
        self.items_article, dummy = self.retrieve_documents(
            'article', search_terms, max_docs, batch_size, sort_type, sort_order
        )
        dummy, self.items_patent = self.retrieve_documents(
            'patent', search_terms, max_docs, batch_size, sort_type, sort_order
        )

        # Log sample of retrieved documents
        logging.info(f"\nSample of retrieved articles:")
        for i, item in enumerate(self.items_article[:3]):  # Show first 3 items
            author_names = item.get_author_names()
            authors_count = len(author_names)
            logging.info(f"{i + 1}. {item.title} ({item.year}) - {authors_count} authors")

        logging.info(f"\nSample of retrieved patents:")
        for i, item in enumerate(self.items_patent[:3]):  # Show first 3 items
            author_names = item.get_author_names()
            authors_count = len(author_names)
            logging.info(f"{i + 1}. {item.title} ({item.year}) - {authors_count} inventors")

        # Save all items to pickle files
        # Articles first
        FileUtils.save_pickle_file(FileUtils.get_articles_pickle_file(self.case), self.items_article)
        # Patents second
        FileUtils.save_pickle_file(FileUtils.get_patents_pickle_file(self.case), self.items_patent)

    def search_by_query(self, query_dict: dict, max_docs: int = 10000, 
                       batch_size: int = 10) -> Tuple[List[ItemDocument], List[ItemDocument]]:
        """
        Retrieve documents using a custom Elasticsearch query.

        Args:
            query_dict: Custom Elasticsearch query dictionary
            max_docs: Maximum number of documents to retrieve
            batch_size: Number of documents to retrieve per batch

        Returns:
            Tuple of (articles, patents) as lists of ItemDocument objects
        """
        url = es_url_via_cassandra
        processed_count = 0
        items_article = []
        items_patent = []

        for from_idx in range(0, max_docs, batch_size):
            # Add pagination to the custom query
            paginated_query = query_dict.copy()
            paginated_query["from"] = from_idx
            paginated_query["size"] = batch_size

            # Send post request
            response = requests.post(url, json=paginated_query)

            if response.status_code == 200:
                data = response.json()
                hits = data.get("hits", {}).get("hits", [])

                if not hits:
                    logging.info(f"No more results found after {processed_count} documents.")
                    break

                logging.info(f"Processing batch {from_idx // batch_size + 1} (documents {from_idx + 1}-{from_idx + len(hits)})")

                for hit in hits:
                    source = hit.get("_source", {})
                    doc_type = source.get("type")
                    doc_id = source.get("id")

                    try:
                        item = ItemDocument.from_elasticsearch_source(source)
                        
                        if not item.title or not item.summary:
                            logging.warning(f"Skipping document {doc_id} - missing title or summary")
                            continue

                        if doc_type == 'article':
                            items_article.append(item)
                        elif doc_type == 'patent':
                            items_patent.append(item)
                        else:
                            logging.warning(f"Unknown document type: {doc_type} for document {doc_id}")

                        processed_count += 1

                    except Exception as e:
                        logging.error(f"Error processing document {doc_id}: {e}")
                        continue

            else:
                logging.error(f"Elasticsearch error: {response.status_code} - {response.text}")
                break

        logging.info(f"Retrieved {len(items_article)} articles and {len(items_patent)} patents using custom query.")
        return items_article, items_patent

    def get_document_by_id(self, doc_id: str) -> ItemDocument:
        """
        Retrieve a single document by its ID.

        Args:
            doc_id: Document ID to retrieve

        Returns:
            ItemDocument object or None if not found
        """
        url = es_url_via_cassandra
        
        query = {
            "query": {
                "term": {
                    "id": doc_id
                }
            },
            "size": 1
        }

        response = requests.post(url, json=query)

        if response.status_code == 200:
            data = response.json()
            hits = data.get("hits", {}).get("hits", [])
            
            if hits:
                source = hits[0].get("_source", {})
                return ItemDocument.from_elasticsearch_source(source)
            else:
                logging.warning(f"Document with ID {doc_id} not found")
                return None
        else:
            logging.error(f"Elasticsearch error: {response.status_code} - {response.text}")
            return None
