import os
from sentence_transformers import SentenceTransformer
from huggingface_hub import login
import chromadb
from tqdm import tqdm

from src.utils.constants import articles_name, patents_name
from src.utils.file_utils import FileUtils
from src.utils.logger import Logger
from src.utils.article_utils import ItemArticle, ItemPatent

logging = Logger(__name__).get()

class CreateVectorStore:
    def __init__(self, case: str):
        self.case = case
        self.client = None
        self.article_collection_name = articles_name
        self.patent_collection_name = patents_name
        self.model = SentenceTransformer('sentence-transformers/all-MiniLM-L6-v2')
        self.hf_token = os.environ['HF_TOKEN']
        login(self.hf_token, add_to_git_credential = True)

    @staticmethod
    def description(item):
        text = item.title + ' ' + item.summary
        return text

    def delete_collection(self, collection_name):
        existing_collection_names = [collection.name for collection in self.client.list_collections()]
        if collection_name in existing_collection_names:
            self.client.delete_collection(collection_name)
            logging.info(f"Deleted existing collection: {collection_name}")

    def create_vector_store(self):
        # Load all items from pickle files
        items_article = FileUtils.load_items(FileUtils.get_articles_pickle_file(self.case))
        items_patent = FileUtils.load_items(FileUtils.get_patents_pickle_file(self.case))
        logging.info(f"Loaded {len(items_article)} articles and {len(items_patent)} patents.")

        # Create vector store
        logging.info(f"Creating vector store...")
        self.client = chromadb.PersistentClient(path = FileUtils.get_vectorstore_db_file(self.case))

        # Delete existing collections if they exist
        self.delete_collection(self.article_collection_name)
        self.delete_collection(self.patent_collection_name)

        # Create new collections
        article_collection = self.client.create_collection(self.article_collection_name)
        patent_collection = self.client.get_or_create_collection(self.patent_collection_name)

        # Add articles to vector store
        print(f"Loading article items to vector store...")
        for i in tqdm(range(0, len(items_article), 100)):
            documents = [CreateVectorStore.description(item) for item in items_article[i: i + 100]]
            vectors = self.model.encode(documents).astype(float).tolist()
            metadata = [{"id": item.id, "title": item.title, "abstract": item.summary,
                         "authors": ", ".join(item.authors) if item.authors else "", "type": "article", "year": item.year,
                         "url": item.urls.empty if "" else item.urls[0]} for item in items_article[i: i + 100]]
            ids = [f"doc_{j}" for j in range(i, i + metadata.__len__())]
            article_collection.add(
                ids = ids,
                documents = documents,
                embeddings = vectors,
                metadatas = metadata
            )

        for i in tqdm(range(0, len(items_patent), 100)):
            documents = [CreateVectorStore.description(item) for item in items_patent[i: i + 100]]
            vectors = self.model.encode(documents).astype(float).tolist()
            metadata = [{"id": item.id, "title": item.title, "abstract": item.summary,
                         "authors": ", ".join(item.inventors) if item.inventors else "", "type": "patent", "year": item.year,
                         "url": f"https://worldwide.espacenet.com/patent/search?q=" + item.id} for item in
                        items_patent[i: i + 100]]
            ids = [f"doc_{j}" for j in range(i, i + metadata.__len__())]
            patent_collection.add(
                ids=ids,
                documents=documents,
                embeddings=vectors,
                metadatas=metadata
            )
