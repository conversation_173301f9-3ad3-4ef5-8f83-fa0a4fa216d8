from flask import Flask, request, jsonify
from flask_cors import CORS

from src.rag.rag_chat_service import RagChatService
from src.utils.logger import Logger

logging = Logger(__name__).get()

class RestRagChatService(RagChatService):
    def __init__(self, case: str):
        super().__init__(case)
        self.app = Flask(__name__)
        CORS(self.app)  # Enable CORS for all routes
        self._setup_routes()

    def _setup_routes(self):
        """Setup Flask routes for the REST API."""

        @self.app.route('/health', methods=['GET'])
        def health_check():
            """Health check endpoint."""
            return jsonify({
                "status": "healthy",
                "message": "RAG Chat REST API is running",
                "service": "RestRagChat"
            })

        @self.app.route('/query', methods=['POST'])
        def query_endpoint():
            """
            Main query endpoint that accepts a message and returns JSON response.

            Expected JSON payload:
            {
                "message": "Your question here"
            }

            Returns:
            {
                "reply": "AI response text",
                "documents": ["list of similar documents"],
                "metadata": [{"metadata objects"}],
                "query": "original query",
                "sources_count": number_of_sources
            }
            """
            try:
                # Get JSON data from request
                data = request.get_json()

                if not data or 'message' not in data:
                    return jsonify({
                        "error": "Missing 'message' field in request body"
                    }), 400

                message = data['message'].strip()

                if not message:
                    return jsonify({
                        "error": "Message cannot be empty"
                    }), 400

                logging.info(f"Processing query: {message}")

                # Process the query using RAG
                reply, documents, metadata = self.gpt_4o_mini_rag(message)

                logging.info(f"Query processed successfully. Found {len(documents)} sources.")

                return jsonify({
                    "reply": reply,
                    "documents": documents,
                    "metadata": metadata,
                    "query": message,
                    "sources_count": len(documents)
                })

            except Exception as e:
                logging.error(f"Error processing query: {str(e)}")
                return jsonify({
                    "error": f"Internal server error: {str(e)}"
                }), 500

        @self.app.route('/query_simple', methods=['GET'])
        def query_simple_endpoint():
            """
            Simple GET endpoint for quick queries via URL parameters.

            Usage: /query_simple?q=your+question+here

            Returns same JSON structure as POST /query
            """
            try:
                message = request.args.get('q', '').strip()

                if not message:
                    return jsonify({
                        "error": "Missing 'q' parameter",
                        "usage": "/query_simple?q=your+question+here"
                    }), 400

                logging.info(f"Processing simple query: {message}")

                # Process the query using RAG
                reply, documents, metadata = self.gpt_4o_mini_rag(message)

                logging.info(f"Simple query processed successfully. Found {len(documents)} sources.")

                return jsonify({
                    "reply": reply,
                    "documents": documents,
                    "metadata": metadata,
                    "query": message,
                    "sources_count": len(documents)
                })

            except Exception as e:
                logging.error(f"Error processing simple query: {str(e)}")
                return jsonify({
                    "error": f"Internal server error: {str(e)}"
                })

        @self.app.route('/', methods=['GET'])
        def index():
            """API documentation endpoint."""
            return jsonify({
                "service": "RAG Chat REST API",
                "description": "Research Assistant REST API with JSON responses",
                "endpoints": {
                    "POST /query": {
                        "description": "Main endpoint for structured queries",
                        "request": {"message": "Your question here"},
                        "response": {
                            "reply": "AI response text",
                            "documents": ["list of similar documents"],
                            "metadata": [{"metadata objects"}],
                            "query": "original query",
                            "sources_count": "number_of_sources"
                        }
                    },
                    "GET /query_simple": {
                        "description": "Simple endpoint with URL parameters",
                        "usage": "/query_simple?q=your+question+here",
                        "response": "Same as POST /query"
                    },
                    "GET /health": {
                        "description": "Health check endpoint"
                    }
                },
                "examples": [
                    "How can spermidine maintain cellular health?",
                    "What is the role of polyamines in cellular processes?",
                    "Explain the relationship between spermidine and autophagy."
                ]
            })

    def run(self, host='0.0.0.0', port=5010, debug=True):
        """Run the Flask REST API server."""
        logging.info("Starting RAG Chat REST API server...")
        logging.info("Available endpoints:")
        logging.info("  - POST /query - Main query endpoint")
        logging.info("  - GET /query_simple?q=question - Simple query endpoint")
        logging.info("  - GET /health - Health check")
        logging.info("  - GET / - API documentation")

        self.app.run(host=host, port=port, debug=debug)
