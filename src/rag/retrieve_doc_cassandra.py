import requests
from cassandra.cluster import Cluster

from src.utils.config import c_host, c_port, c_keyspace, es_url, c_article_table, c_patent_table
from src.utils.file_utils import FileUtils
from src.utils.logger import Logger
from src.utils.article_utils_cassandra import ItemArticle, ItemPatent

logging = Logger(__name__).get()

class RetrieveDoc:
    def __init__(self, case):
        self.items_article = []  # Store all retrieved articles
        self.items_patent = []  # Store all retrieved patents
        self.case = case

    def retrieve_documents(self, search_terms, max_docs = 10000, batch_size = 10):
        """
        Retrieve documents from Elasticsearch and Cassandra using pagination.

        Args:
            search_term: The term to search for in Elasticsearch
            max_docs: Maximum number of documents to retrieve
            batch_size: Number of documents to retrieve per batch

        Returns:
            List of Item objects representing the retrieved articles
        """
        # Elasticsearch endpoint
        url = es_url

        # Cassandra stuff
        cluster = Cluster([c_host], port=c_port)
        session = cluster.connect()
        session.set_keyspace(c_keyspace)
        # Process documents in batches
        processed_count = 0
        items_article = []  # Store all retrieved articles
        items_patent = []  # Store all retrieved patents

        for from_idx in range(0, max_docs, batch_size):
            # Query body with pagination
            query = {
                "from": from_idx,
                "size": batch_size,
                "query": {
                    "bool": {
                        "must": [
                            {
                                "query_string": {
                                    "query": f'lemmas:"{search_term}"'
                                }
                            }
                            for search_term in search_terms
                        ],
                        "should": [],
                        "must_not": []
                    }
                },
                "sort": [
                    {
                        "year": {
                            "order": "desc"
                        }
                    }
                ]
            }

            # Send post request
            response = requests.post(url, json = query)

            # Check for success
            if response.status_code == 200:
                data = response.json()
                hits = data.get("hits", {}).get("hits", [])

                # If no more results, break the loop
                if not hits:
                    logging.info(f"No more results found after {processed_count} documents.")
                    break

                logging.info(f"\nProcessing batch {from_idx // batch_size + 1} (documents {from_idx + 1}-{from_idx + len(hits)})")

                for hit in hits:
                    source = hit.get("_source", {})
                    doc_type = source.get("type")
                    doc_id = source.get("id")
                    logging.info(f"\nES Source id: {doc_id} (type: {doc_type})")

                    if doc_type == 'a':
                        # Query Cassandra
                        try:
                            cass_query = f"SELECT * FROM {c_article_table} WHERE id = %s"
                            result = session.execute(cass_query, [doc_id])
                            cass_result = result.one()
                            if cass_result:
                                logging.debug(f"Processing Cassandra document: {doc_id}")
                                # Create Item from Cassandra row and add to items list
                                item = ItemArticle.from_cassandra_row(cass_result)
                                if item.title and item.summary:
                                    # Add to list
                                    items_article.append(item)
                                    # Generate markdown file
                                    item.to_markdown(FileUtils.get_articles_dir(self.case))
                            else:
                                logging.error(f"No matching record found in Cassandra for id: {doc_id}")
                        except Exception as e:
                            logging.error(f"Error querying Cassandra: {e}")
                    else:
                        if doc_type == 'p':
                            # Query Cassandra
                            try:
                                cass_query = f"SELECT * FROM {c_patent_table} WHERE id = %s"
                                result = session.execute(cass_query, [doc_id])
                                cass_result = result.one()
                                if cass_result:
                                    logging.debug(f"Processing Cassandra document: {doc_id}")
                                    # Create Item from Cassandra row and add to items list
                                    item = ItemPatent.from_cassandra_row(cass_result)
                                    if item.title and item.summary:
                                        # Add to list
                                        items_patent.append(item)
                                        # Generate markdown file
                                        item.to_markdown(FileUtils.get_patents_dir(self.case))
                                else:
                                    logging.error(f"No matching record found in Cassandra for ID: {doc_id}")
                            except Exception as e:
                                logging.error(f"Error querying Cassandra: {e}")
                        else:
                            logging.warning(f"Skipping Cassandra query for document type: {doc_type}")

                    processed_count += 1
            else:
                logging.error(f"Error: {response.status_code} - {response.text}")
                break

        cluster.shutdown()
        logging.info(f"\nCompleted processing {processed_count} documents.")
        logging.info(f"Retrieved {len(items_article)} articles and {len(items_patent)} patents.")

        return items_article, items_patent

    def retrieve_and_store_documents(self, search_term, max_docs = 10000, batch_size = 10):
        search_terms = search_term.split(" ")
        self.items_article, self.items_patent = self.retrieve_documents(search_terms, max_docs, batch_size)

        # You can now work with the items list
        logging.debug(f"\nSample of retrieved articles:")
        for i, item in enumerate(self.items_article[:3]):  # Show first 3 items
            logging.debug(f"{i + 1}. {item.title} ({item.year}) - {len(item.authors)} authors")

        logging.debug(f"\nSample of retrieved patents:")
        for i, item in enumerate(self.items_patent[:3]):  # Show first 3 items
            logging.debug(f"{i + 1}. {item.title} ({item.year}) - {len(item.inventors)} inventors")

        # Save all items to a pickle file
        # Articles first
        FileUtils.save_pickle_file(FileUtils.get_articles_pickle_file(self.case), self.items_article)
        # Patents second
        FileUtils.save_pickle_file(FileUtils.get_patents_pickle_file(self.case), self.items_patent)

